import { notNullish } from '../common'
import type { ErrorCode } from './base-error'
import { buildCauseChain } from './errors'

export interface FormatErrorOptions {
    includeCode?: boolean
    includeCause?: boolean
    maxCauseDepth?: number
    cache?: {
        timeout: number
        storage: Map<Error, { result: string, timestamp: number }>
    }
}

export function formatErrorString(error: Error, options: FormatErrorOptions = {}): string {
    const {
        includeCode = true,
        includeCause = true,
        maxCauseDepth = 3,
        cache,
    } = options

    if (cache) {
        const now = Date.now()
        const cached = cache.storage.get(error)

        if (cached && (now - cached.timestamp) < cache.timeout) {
            return cached.result
        }
    }

    const parts: string[] = []
    const errorWithCode = error as Error & { code?: ErrorCode }

    if (includeCode && notNullish(errorWithCode.code)) {
        parts.push(`[${String(errorWithCode.code)}]`)
    }

    parts.push(`${error.name}: ${error.message}`)

    let result = parts.join(' ')

    if (includeCause && error.cause) {
        const causeChain = buildCauseChain(error.cause, maxCauseDepth)

        if (causeChain) {
            result += `\n  Caused by: ${causeChain}`
        }
    }

    if (cache) {
        cache.storage.set(error, { result, timestamp: Date.now() })
    }

    return result
}

export function createErrorStringCache(timeout = 1000): FormatErrorOptions['cache'] {
    return {
        timeout,
        storage: new Map(),
    }
}
