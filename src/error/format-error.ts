import { notNullish } from '../common'
import type { ErrorCode } from './base-error'
import { buildCauseChain } from './causes'

export interface ErrorStringifyOptions {
    includeCode?: boolean
    includeCause?: boolean
    maxCauseDepth?: number
}

export function stringifyError(error: Error, { includeCode = true, includeCause = true, maxCauseDepth = 3 }: ErrorStringifyOptions = {}): string {
    const parts: string[] = []
    const errorWithCode = error as Error & { code?: ErrorCode }

    if (includeCode && notNullish(errorWithCode.code)) {
        parts.push(`[${String(errorWithCode.code)}]`)
    }

    parts.push(`${error.name}: ${error.message}`)

    let result = parts.join(' ')

    if (includeCause && error.cause) {
        const causeChain = buildCauseChain(error.cause, maxCauseDepth)

        if (causeChain) {
            result += `\n  Caused by: ${causeChain}`
        }
    }

    return result
}
